/// 国际化翻译文件
/// 
/// 包含所有支持语言的翻译文本
library;

import 'package:get/get.dart';

/// 应用翻译
class AppTranslations extends Translations {
  @override
  Map<String, Map<String, String>> get keys => {
    // 中文简体
    'zh_CN': {
      // 通用
      'Flutter Base Framework': 'Flutter 基础框架',
      'ok': '确定',
      'cancel': '取消',
      'confirm': '确认',
      'save': '保存',
      'delete': '删除',
      'edit': '编辑',
      'add': '添加',
      'search': '搜索',
      'loading': '加载中...',
      'retry': '重试',
      'refresh': '刷新',
      'submit': '提交',
      'reset': '重置',
      'clear': '清除',
      'close': '关闭',
      'back': '返回',
      'next': '下一步',
      'previous': '上一步',
      'done': '完成',
      'skip': '跳过',
      'yes': '是',
      'no': '否',

      // 错误信息
      'error': '错误',
      'network_error': '网络连接失败，请检查网络设置',
      'server_error': '服务器错误，请稍后重试',
      'unknown_error': '未知错误',
      'timeout_error': '请求超时，请重试',
      'no_data_found': '暂无数据',
      'load_failed': '加载失败',
      'save_failed': '保存失败',
      'delete_failed': '删除失败',

      // 验证信息
      'required': '此项为必填项',
      'invalid_email': '请输入有效的邮箱地址',
      'invalid_phone': '请输入有效的手机号码',
      'password_too_short': '密码长度至少6位',
      'password_mismatch': '两次输入的密码不一致',
      'invalid_format': '格式不正确',

      // 认证相关
      'login': '登录',
      'logout': '退出登录',
      'register': '注册',
      'forgot_password': '忘记密码',
      'reset_password': '重置密码',
      'change_password': '修改密码',
      'email': '邮箱',
      'password': '密码',
      'confirm_password': '确认密码',
      'username': '用户名',
      'phone': '手机号',
      'verification_code': '验证码',
      'send_code': '发送验证码',
      'resend_code': '重新发送',
      'login_success': '登录成功',
      'login_failed': '登录失败',
      'register_success': '注册成功',
      'register_failed': '注册失败',
      'logout_confirm': '确定要退出登录吗？',

      // 用户资料
      'profile': '个人资料',
      'personal_info': '个人信息',
      'avatar': '头像',
      'nickname': '昵称',
      'gender': '性别',
      'birthday': '生日',
      'address': '地址',
      'bio': '个人简介',
      'male': '男',
      'female': '女',
      'other': '其他',
      'update_profile': '更新资料',
      'update_success': '更新成功',
      'update_failed': '更新失败',

      // 设置
      'settings': '设置',
      'general': '通用',
      'account': '账户',
      'security': '安全',
      'privacy': '隐私',
      'notification': '通知',
      'language': '语言',
      'theme': '主题',
      'light_theme': '浅色主题',
      'dark_theme': '深色主题',
      'system_theme': '跟随系统',
      'about': '关于',
      'version': '版本',
      'feedback': '意见反馈',
      'help': '帮助',

      // 权限相关
      'permission': '权限',
      'permission_denied': '权限被拒绝',
      'permission_required': '需要权限',
      'grant_permission': '授予权限',
      'camera_permission': '相机权限',
      'storage_permission': '存储权限',
      'location_permission': '位置权限',
      'microphone_permission': '麦克风权限',
      'contacts_permission': '通讯录权限',
      'notification_permission': '通知权限',

      // 时间相关
      'today': '今天',
      'yesterday': '昨天',
      'tomorrow': '明天',
      'this_week': '本周',
      'this_month': '本月',
      'this_year': '今年',

      // 动态文本
      'item_count': '共 @count 项',
      'time_ago': '@time前',
      'welcome_user': '欢迎，@name',
      'file_size': '文件大小：@size',
      'page_info': '第 @current 页，共 @total 页',

      // 占位符文本
      'enter_email': '请输入邮箱地址',
      'enter_password': '请输入密码',
      'enter_phone': '请输入手机号',
      'enter_nickname': '请输入昵称',
      'enter_message': '请输入消息',
      'search_hint': '搜索...',
      'no_data': '暂无数据',
      'empty_list': '列表为空',
      'coming_soon': '敬请期待',
    },

    // 中文繁体
    'zh_TW': {
      // 通用
      'Flutter Base Framework': 'Flutter 基礎框架',
      'ok': '確定',
      'cancel': '取消',
      'confirm': '確認',
      'save': '保存',
      'delete': '刪除',
      'edit': '編輯',
      'add': '添加',
      'search': '搜索',
      'loading': '加載中...',
      'retry': '重試',
      'refresh': '刷新',
      'submit': '提交',
      'reset': '重置',
      'clear': '清除',
      'close': '關閉',
      'back': '返回',
      'next': '下一步',
      'previous': '上一步',
      'done': '完成',
      'skip': '跳過',
      'yes': '是',
      'no': '否',

      // 错误信息
      'error': '錯誤',
      'network_error': '網絡連接失敗，請檢查網絡設置',
      'server_error': '服務器錯誤，請稍後重試',
      'unknown_error': '未知錯誤',
      'timeout_error': '請求超時，請重試',
      'no_data_found': '暫無數據',
      'load_failed': '加載失敗',
      'save_failed': '保存失敗',
      'delete_failed': '刪除失敗',

      // 验证信息
      'required': '此項為必填項',
      'invalid_email': '請輸入有效的郵箱地址',
      'invalid_phone': '請輸入有效的手機號碼',
      'password_too_short': '密碼長度至少6位',
      'password_mismatch': '兩次輸入的密碼不一致',
      'invalid_format': '格式不正確',

      // 认证相关
      'login': '登錄',
      'logout': '退出登錄',
      'register': '註冊',
      'forgot_password': '忘記密碼',
      'reset_password': '重置密碼',
      'change_password': '修改密碼',
      'email': '郵箱',
      'password': '密碼',
      'confirm_password': '確認密碼',
      'username': '用戶名',
      'phone': '手機號',
      'verification_code': '驗證碼',
      'send_code': '發送驗證碼',
      'resend_code': '重新發送',
      'login_success': '登錄成功',
      'login_failed': '登錄失敗',
      'register_success': '註冊成功',
      'register_failed': '註冊失敗',
      'logout_confirm': '確定要退出登錄嗎？',

      // 用户资料
      'profile': '個人資料',
      'personal_info': '個人信息',
      'avatar': '頭像',
      'nickname': '昵稱',
      'gender': '性別',
      'birthday': '生日',
      'address': '地址',
      'bio': '個人簡介',
      'male': '男',
      'female': '女',
      'other': '其他',
      'update_profile': '更新資料',
      'update_success': '更新成功',
      'update_failed': '更新失敗',

      // 设置
      'settings': '設置',
      'general': '通用',
      'account': '賬戶',
      'security': '安全',
      'privacy': '隱私',
      'notification': '通知',
      'language': '語言',
      'theme': '主題',
      'light_theme': '淺色主題',
      'dark_theme': '深色主題',
      'system_theme': '跟隨系統',
      'about': '關於',
      'version': '版本',
      'feedback': '意見反饋',
      'help': '幫助',

      // 权限相关
      'permission': '權限',
      'permission_denied': '權限被拒絕',
      'permission_required': '需要權限',
      'grant_permission': '授予權限',
      'camera_permission': '相機權限',
      'storage_permission': '存儲權限',
      'location_permission': '位置權限',
      'microphone_permission': '麥克風權限',
      'contacts_permission': '通訊錄權限',
      'notification_permission': '通知權限',

      // 时间相关
      'today': '今天',
      'yesterday': '昨天',
      'tomorrow': '明天',
      'this_week': '本週',
      'this_month': '本月',
      'this_year': '今年',

      // 动态文本
      'item_count': '共 @count 項',
      'time_ago': '@time前',
      'welcome_user': '歡迎，@name',
      'file_size': '文件大小：@size',
      'page_info': '第 @current 頁，共 @total 頁',

      // 占位符文本
      'enter_email': '請輸入郵箱地址',
      'enter_password': '請輸入密碼',
      'enter_phone': '請輸入手機號',
      'enter_nickname': '請輸入昵稱',
      'enter_message': '請輸入消息',
      'search_hint': '搜索...',
      'no_data': '暫無數據',
      'empty_list': '列表為空',
      'coming_soon': '敬請期待',
    },

    // 英文
    'en_US': {
      // 通用
      'Flutter Base Framework': 'Flutter Base Framework',
      'ok': 'OK',
      'cancel': 'Cancel',
      'confirm': 'Confirm',
      'save': 'Save',
      'delete': 'Delete',
      'edit': 'Edit',
      'add': 'Add',
      'search': 'Search',
      'loading': 'Loading...',
      'retry': 'Retry',
      'refresh': 'Refresh',
      'submit': 'Submit',
      'reset': 'Reset',
      'clear': 'Clear',
      'close': 'Close',
      'back': 'Back',
      'next': 'Next',
      'previous': 'Previous',
      'done': 'Done',
      'skip': 'Skip',
      'yes': 'Yes',
      'no': 'No',

      // 错误信息
      'error': 'Error',
      'network_error': 'Network connection failed, please check your network settings',
      'server_error': 'Server error, please try again later',
      'unknown_error': 'Unknown error',
      'timeout_error': 'Request timeout, please try again',
      'no_data_found': 'No data found',
      'load_failed': 'Load failed',
      'save_failed': 'Save failed',
      'delete_failed': 'Delete failed',

      // 验证信息
      'required': 'This field is required',
      'invalid_email': 'Please enter a valid email address',
      'invalid_phone': 'Please enter a valid phone number',
      'password_too_short': 'Password must be at least 6 characters',
      'password_mismatch': 'Passwords do not match',
      'invalid_format': 'Invalid format',

      // 认证相关
      'login': 'Login',
      'logout': 'Logout',
      'register': 'Register',
      'forgot_password': 'Forgot Password',
      'reset_password': 'Reset Password',
      'change_password': 'Change Password',
      'email': 'Email',
      'password': 'Password',
      'confirm_password': 'Confirm Password',
      'username': 'Username',
      'phone': 'Phone',
      'verification_code': 'Verification Code',
      'send_code': 'Send Code',
      'resend_code': 'Resend',
      'login_success': 'Login successful',
      'login_failed': 'Login failed',
      'register_success': 'Registration successful',
      'register_failed': 'Registration failed',
      'logout_confirm': 'Are you sure you want to logout?',

      // 用户资料
      'profile': 'Profile',
      'personal_info': 'Personal Information',
      'avatar': 'Avatar',
      'nickname': 'Nickname',
      'gender': 'Gender',
      'birthday': 'Birthday',
      'address': 'Address',
      'bio': 'Bio',
      'male': 'Male',
      'female': 'Female',
      'other': 'Other',
      'update_profile': 'Update Profile',
      'update_success': 'Update successful',
      'update_failed': 'Update failed',

      // 设置
      'settings': 'Settings',
      'general': 'General',
      'account': 'Account',
      'security': 'Security',
      'privacy': 'Privacy',
      'notification': 'Notification',
      'language': 'Language',
      'theme': 'Theme',
      'light_theme': 'Light Theme',
      'dark_theme': 'Dark Theme',
      'system_theme': 'System Theme',
      'about': 'About',
      'version': 'Version',
      'feedback': 'Feedback',
      'help': 'Help',

      // 权限相关
      'permission': 'Permission',
      'permission_denied': 'Permission denied',
      'permission_required': 'Permission required',
      'grant_permission': 'Grant Permission',
      'camera_permission': 'Camera Permission',
      'storage_permission': 'Storage Permission',
      'location_permission': 'Location Permission',
      'microphone_permission': 'Microphone Permission',
      'contacts_permission': 'Contacts Permission',
      'notification_permission': 'Notification Permission',

      // 时间相关
      'today': 'Today',
      'yesterday': 'Yesterday',
      'tomorrow': 'Tomorrow',
      'this_week': 'This Week',
      'this_month': 'This Month',
      'this_year': 'This Year',

      // 动态文本
      'item_count': '@count items',
      'time_ago': '@time ago',
      'welcome_user': 'Welcome, @name',
      'file_size': 'File size: @size',
      'page_info': 'Page @current of @total',

      // 占位符文本
      'enter_email': 'Enter email address',
      'enter_password': 'Enter password',
      'enter_phone': 'Enter phone number',
      'enter_nickname': 'Enter nickname',
      'enter_message': 'Enter message',
      'search_hint': 'Search...',
      'no_data': 'No data',
      'empty_list': 'Empty list',
      'coming_soon': 'Coming soon',
    },
  };
}
